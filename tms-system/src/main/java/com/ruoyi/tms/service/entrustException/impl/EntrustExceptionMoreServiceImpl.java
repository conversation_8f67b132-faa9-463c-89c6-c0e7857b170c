package com.ruoyi.tms.service.entrustException.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.tms.constant.finance.PayDetailStatusEnum;
import com.ruoyi.tms.constant.finance.ReceiveDetailStatusEnum;
import com.ruoyi.tms.domain.basic.Carrier;
import com.ruoyi.tms.domain.basic.LockCarrierHistory;
import com.ruoyi.tms.domain.carrier.Entrust;
import com.ruoyi.tms.domain.carrier.EntrustExp;
import com.ruoyi.tms.domain.carrier.EntrustExpLossRecord;
import com.ruoyi.tms.domain.carrier.EntrustLot;
import com.ruoyi.tms.domain.entrustException.*;
import com.ruoyi.tms.domain.finance.OtherFee;
import com.ruoyi.tms.domain.finance.PayDetail;
import com.ruoyi.tms.domain.finance.ReceiveDetail;
import com.ruoyi.tms.domain.invoice.Invoice;
import com.ruoyi.tms.domain.trace.Allocation;
import com.ruoyi.tms.domain.utils.StringUtil;
import com.ruoyi.tms.mapper.basic.CarrierMapper;
import com.ruoyi.tms.mapper.carrier.EntrustExpMapper;
import com.ruoyi.tms.mapper.carrier.EntrustLotMapper;
import com.ruoyi.tms.mapper.carrier.EntrustMapper;
import com.ruoyi.tms.mapper.entrustException.*;
import com.ruoyi.tms.mapper.finance.OtherFeeMapper;
import com.ruoyi.tms.mapper.finance.PayDetailMapper;
import com.ruoyi.tms.mapper.finance.ReceiveDetailMapper;
import com.ruoyi.tms.mapper.invoice.InvoiceMapper;
import com.ruoyi.tms.mapper.trace.AllocationMapper;
import com.ruoyi.tms.mapper.trace.TEntrustExpMapper;
import com.ruoyi.tms.service.entrustException.IEntrustExceptionMoreService;
import com.ruoyi.tms.service.finance.IOtherFeeService;
import com.ruoyi.tms.service.finance.IPayDetailService;
import com.ruoyi.tms.service.finance.IReceiveDetailService;
import com.ruoyi.tms.service.trace.IAllocationService;
import com.ruoyi.tms.vo.finance.ReceiveDetailVO;
import com.ruoyi.tms.vo.finance.exception.ReceiveDetailException;
import com.ruoyi.tms.vo.trace.AjaxParamsVO;
import com.ruoyi.tms.vo.trace.ExceptionCorrectionVO;
import com.ruoyi.tms.vo.trace.ResidualValueTrackingVO;
import com.ruoyi.util.ShiroUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EntrustExceptionMoreServiceImpl implements IEntrustExceptionMoreService {

    @Autowired
    private TInsuranceMapper insuranceMapper;
    @Autowired
    private TExceptionTrackMapper exceptionTrackMapper;
    @Autowired
    private EntrustExpMapper entrustExpMapper;
    @Autowired
    private TExpEntrustMoreMapper expEntrustMoreMapper;
    @Autowired
    private EntrustLotMapper entrustLotMapper;
    @Autowired
    private TExceptionDictMapper exceptionDictMapper;
    @Autowired
    private IReceiveDetailService receiveDetailService;
    @Autowired
    private IPayDetailService payDetailService;
    @Autowired
    private InvoiceMapper invoiceMapper;
    @Autowired
    private TExpInvoiceMapper expInvoiceMapper;
    @Autowired
    private TEntrustExpMapper tEntrustExpMapper;
    @Autowired
    private ResidualValueTrackingMapper residualValueTrackingMapper;
    @Autowired
    private CarrierMapper carrierMapper;
    @Autowired
    private IAllocationService allocationService;
    @Autowired
    private AllocationMapper allocationMapper;
    @Autowired
    private EntrustMapper entrustMapper;
    @Autowired
    private MAppraisersMapper appraisersMapper;
    @Autowired
    private MExceptionCorrectionMapper exceptionCorrectionMapper;
    @Autowired
    private ReceiveDetailMapper receiveDetailMapper;
    @Autowired
    private ShiroUtils shiroUtils;
    @Autowired
    private OtherFeeMapper otherFeeMapper;
    @Autowired
    private InsuranceRecordMapper insuranceRecordMapper;


    /**
     * 新增异常
     * @param ajaxParamsVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addEntrustException(AjaxParamsVO ajaxParamsVO) {
        String pageId = ajaxParamsVO.getPageId();
        if(StringUtils.isBlank(pageId)){
            pageId = "abnormal_temp";
        }
        int i = 0;
        //保存主异常表   EntrustExp
        EntrustExp entrustExp = ajaxParamsVO.getEntrustExp();
        if(entrustExp.getIsCustomerException() == 1) {
            TEntrustExpExample entrustExpExample = new TEntrustExpExample();
            entrustExpExample.createCriteria().andEntrustIdEqualTo(entrustExp.getEntrustId()).andDelFlagEqualTo((short) 0);
            List<TEntrustExp> tEntrustExps = tEntrustExpMapper.selectByExample(entrustExpExample);
            //验证该单是否添加异常  避免重复添加应收 应付明细
            if(ObjectUtil.isNotEmpty(tEntrustExps)) {
                throw new BusinessException(com.ruoyi.common.utils.StringUtils.format("该单已添加异常,请勿重复添加!"));
            }
            entrustExp.setRegDate(new Date()); //
            entrustExp.setRegUserId(shiroUtils.getUserId().toString());
            entrustExp.setRegUserName(shiroUtils.getSysUser().getUserName());
            entrustExp.setRegScrId(pageId); //新增画面id
            entrustExp.setCorScrId(pageId);
            entrustExp.setCorDate(new Date());
            entrustExp.setCorUserName(shiroUtils.getSysUser().getUserName());
            entrustExp.setCorUserId(shiroUtils.getUserId().toString());
            //entrustExp.setEntrustExpId(IdUtil.simpleUUID());
            entrustExp.setCaseStatus("1");   //案件状态，1：新建
            entrustExp.setIsCustomerException((short) 1);
            i = entrustExpMapper.insertEntrustExp(entrustExp);
            if(i == 0) {
                throw new BusinessException("添加异常失败!");
            }
            //保存损失金额记录
            if(entrustExp.getLossAmount() != null && entrustExp.getLossAmount().compareTo(BigDecimal.ZERO) != 0) {
                EntrustExpLossRecord entrustExpLossRecord = new EntrustExpLossRecord();
                entrustExpLossRecord.setId(IdUtil.simpleUUID());
                entrustExpLossRecord.setEntrustExpId(entrustExp.getEntrustExpId());
                entrustExpLossRecord.setRegUserName(shiroUtils.getSysUser().getUserName());
                entrustExpLossRecord.setLossAmount(entrustExp.getLossAmount());
                entrustExpLossRecord.setLossMemo(entrustExp.getLossMemo());
                entrustExpMapper.insertEntrustExpLossRecord(entrustExpLossRecord);
            }
            //Entrust entrust = entrustMapper.selectEntrustById(entrustExp.getEntrustId());
            //前提数据量不大  暂时循环插入
            //保存主异常和异常类型的关联表
            String expType = entrustExp.getExpType();
            String[] split_expType = expType.split(",");
            for (String s : split_expType) {
                TExceptionDict exceptionDict = new TExceptionDict();
                exceptionDict.setId(IdUtil.simpleUUID());
                exceptionDict.setEntrustExpId(entrustExp.getEntrustExpId());
                exceptionDict.setDictCode((Short.parseShort(s)));
                exceptionDictMapper.insertSelective(exceptionDict);
            }
            //保存主异常和发货单之间的关联关系;  应收明细表中存在发货单和应收明细的关联关系
            List<String> invoiceIds = ajaxParamsVO.getInvoiceIds();
            if(invoiceIds.size() > 0) {
                for (String invoiceId : invoiceIds) {
                    String invId = invoiceId.split("-")[0];
                    String adjustAmount = invoiceId.split("-")[1];
                    String adjustType = invoiceId.split("-")[2];    //add:增加 deduct:扣减
                    String isProfitIncluded = invoiceId.split("-")[3];   //0:不纳入利润计算 1:纳入利润计算

                    TExpInvoice expInvoice = new TExpInvoice();
                    expInvoice.setId(IdUtil.simpleUUID());
                    expInvoice.setExpId(entrustExp.getEntrustExpId());
                    expInvoice.setInvoiceId(invId);
                    expInvoiceMapper.insertSelective(expInvoice);
                    //查找发货单信息
                    Invoice invoice = invoiceMapper.selectInvoiceById(invId);

                    /*
                     * 锁定三方
                     */
                    OtherFee otherFeeUpdate = new OtherFee();
                    otherFeeUpdate.setLockOtherFee(entrustExp.getLockOtherFee());
                    otherFeeMapper.updateByLotId(otherFeeUpdate, invoice.getInvoiceId());

                    //新建应收明细
                    if(StringUtils.isNotEmpty(adjustAmount)) {
                        if(!adjustAmount.equals("0")) {
                            BigDecimal transFeeCount;
                            if ("add".equals(adjustType)) {
                                transFeeCount = new BigDecimal(adjustAmount);
                            }else {
                                transFeeCount = new BigDecimal(adjustAmount).negate();  //扣款存负数
                            }

                            int payoutMark = 1; //0:不是赔款 1:是赔款
//                            if ("1".equals(isProfitIncluded)) {
//                                payoutMark = 0;
//                            }else {
//                                payoutMark = 1;
//                            }

                            //应付费用完善 发货单信息完善
                            ReceiveDetailVO receiveDetail = new ReceiveDetailVO();
                            receiveDetail.setReceiveDetailId(IdUtil.simpleUUID());
                            receiveDetail.setVbillno(receiveDetailService.createReceiveDetailVbillno("1".equals(invoice.getIsFleetData()) ? 1 : 0));
                            receiveDetail.setFreeType("0");//费用类型  0:在途 1:运费
                            receiveDetail.setTransFeeCount(transFeeCount);
                            receiveDetail.setUngotAmount(transFeeCount);
                            receiveDetail.setInvoiceId(invoice.getInvoiceId());
                            receiveDetail.setInvoiceVbillno(invoice.getVbillno());
                            receiveDetail.setCustomerId(invoice.getCustomerId());
                            receiveDetail.setCustCode(invoice.getCustCode());
                            receiveDetail.setCustName(invoice.getCustAbbr());//客户简称
                            receiveDetail.setSalesDept(invoice.getSalesDept());
                            receiveDetail.setBalaDept(invoice.getBalaDept());
                            receiveDetail.setBalaCustomer(invoice.getBalaCustomerId());
                            receiveDetail.setBalatype(invoice.getBalaType());
                            receiveDetail.setBalaCode(invoice.getBalaCode());
                            receiveDetail.setBalaName(invoice.getBalaName());
                            receiveDetail.setBalaCorp(invoice.getBalaCorpId());//字典bala_corp
                            receiveDetail.setNumCount(invoice.getNumCount());
                            receiveDetail.setVolumeCount(invoice.getVolumeCount());
                            receiveDetail.setFeeWeightCount(invoice.getWeightCount());
                            receiveDetail.setReqDeliDate(invoice.getReqDeliDate());
                            receiveDetail.setReqArriDate(invoice.getReqArriDate());
                            receiveDetail.setVbillstatus(ReceiveDetailStatusEnum.AFFIRM.getValue()); //应收单据状态 默认新建
                            receiveDetail.setPayoutMark(payoutMark);//异常应收扣款标志
                            receiveDetail.setRegScrId(pageId);
                            receiveDetail.setCorScrId(pageId);
                            receiveDetail.setIsFleetData(invoice.getIsFleetData());
                            receiveDetail.setIsFleetAssign(invoice.getIsFleetAssign());
                            receiveDetailService.insertReceiveDetailAndAdjustRecord(receiveDetail,false);
                            //receiveDetailService.insertReceiveDetail(receiveDetail);
                        }
                    }
                }
            }


            List<AjaxParamsVO.LockCarrier> lockCarrierList = ajaxParamsVO.getLockCarrierList();
            for (AjaxParamsVO.LockCarrier lockCarrier : lockCarrierList) {
                Carrier carrier = carrierMapper.selectCarrierById(lockCarrier.getCarrierId());
                if (lockCarrier.getLockPay() != null && !lockCarrier.getLockPay().equals(carrier.getLockPay())) {
                    //保存承运商锁定信息
                    LockCarrierHistory lockCarrierHistory = new LockCarrierHistory();
                    lockCarrierHistory.setId(IdUtil.simpleUUID());
                    lockCarrierHistory.setCarrierId(lockCarrier.getCarrierId());
                    lockCarrierHistory.setCarrierName(carrier.getCarrName());
                    lockCarrierHistory.setLockUserName(shiroUtils.getSysUser().getUserName());
                    lockCarrierHistory.setLockTime(new Date());
                    lockCarrierHistory.setLockType(lockCarrier.getLockPay());
                    lockCarrierHistory.setLockReason("新添异常：" + entrustExp.getEntrustId() + "/" + lockCarrier.getLockPay());
                    carrierMapper.insertLockCarrierHistory(lockCarrierHistory);

                    //设置整合信息
                    String lockPayReason = DateFormatUtils.format(new Date(), "yyyy-MM-dd")+"/"+shiroUtils.getSysUser().getUserName()+"/"+lockCarrierHistory.getLockReason();

                    Carrier carrierUpdate = new Carrier();
                    carrierUpdate.setLockPayReason(lockCarrier.getLockPayReason());
                    carrierUpdate.setLockPayReasonUnion(lockPayReason);
                    carrierUpdate.setLockPay(lockCarrier.getLockPay());
                    carrierUpdate.setCarrierId(lockCarrier.getCarrierId());
                    carrierUpdate.setCorDate(new Date());
                    carrierUpdate.setCorScrId(pageId);
                    carrierMapper.updateCarrier(carrierUpdate);
                }
            }

            //保存运单
            List<String> lotIdTransFeeList = ajaxParamsVO.getLotIdTransFeeList();
            for (int j = 0; j < lotIdTransFeeList.size(); j++) {  //一个运单保存一条异常记录  lotnolist:["BAT202211245122-1500.50","BAT202211165086-1200.00","BAT202211155082-","BAT202208204802-"]
                if(StringUtils.isNotEmpty(lotIdTransFeeList.get(j))) {
                    //根据运单号查询对应的承运商信息  以及该运单对应的车辆和司机信息
                    String[] split = lotIdTransFeeList.get(j).split(",");
                    for (int k = 0; k < split.length; k++) {
                        String lot_vbillno = split[k];
                        EntrustLot entrustLot = entrustLotMapper.selectEntrustLotById(lot_vbillno.split("-")[0]);
                        //entrustLotMapper.sele
                        //查询运单对应的所有委托单
                        List<Entrust> entrusts = entrustMapper.selectEntrustByLotId(entrustLot.getEntrustLotId());
                        //如果勾选了锁定应付  那么所有运单中的lock_pay字段也锁定
                        if(entrustExp.getLockPay() == 1) {
                            entrustLot.setLockPay("1");
                            entrustLot.setCorDate(new Date());
                            entrustLot.setCorScrId(pageId);
                            entrustLot.setCorUserId(shiroUtils.getUserId().toString());
                            entrustLotMapper.updateEntrustLot(entrustLot);
                        }
                        //如果勾选了锁定承运商
                   /*     if(entrustExp.getLockPay() == 2) {
                            //保存承运商锁定信息
                            LockCarrierHistory lockCarrierHistory = new LockCarrierHistory();
                            lockCarrierHistory.setId(IdUtil.simpleUUID());
                            lockCarrierHistory.setCarrierId(entrustLot.getCarrierId());
                            lockCarrierHistory.setCarrierName(entrustLot.getCarrierName());
                            lockCarrierHistory.setLockUserName(shiroUtils.getSysUser().getUserName());
                            lockCarrierHistory.setLockTime(new Date());
                            lockCarrierHistory.setLockReason("异常跟踪/"+entrustLot.getLot()+"/"+entrustExp.getHandleNote());
                            carrierMapper.insertLockCarrierHistory(lockCarrierHistory);

                            //设置整合信息
                            String lockPayReason = DateFormatUtils.format(new Date(), "yyyy-MM-dd")+"/"+shiroUtils.getSysUser().getUserName()+"/"+lockCarrierHistory.getLockReason();

                            Carrier carrier = new Carrier();
                            carrier.setLockPayReason(lockCarrierHistory.getLockReason());
                            carrier.setLockPayReasonUnion(lockPayReason);
                            carrier.setLockPay(1);
                            carrier.setCarrierId(entrustLot.getCarrierId());
                            carrier.setCorDate(new Date());
                            carrier.setCorScrId(pageId);
                            carrierMapper.updateCarrier(carrier);
                        }*/
                        TExpEntrustMore expEntrustMore = new TExpEntrustMore();
                        expEntrustMore.setRegDate(new Date());
                        expEntrustMore.setRegUserId(shiroUtils.getUserId().toString());
                        expEntrustMore.setRegUserName(shiroUtils.getSysUser().getUserName());
                        expEntrustMore.setRegScrId(pageId); //新增画面id
                        expEntrustMore.setCorScrId(pageId); //新增画面id
                        expEntrustMore.setCorDate(new Date());
                        expEntrustMore.setCorUserName(shiroUtils.getSysUser().getUserName());
                        expEntrustMore.setCorUserId(shiroUtils.getUserId().toString());
                        expEntrustMore.setId(IdUtil.simpleUUID());
                        entrustLot.setEntrustLotId(entrustLot.getEntrustLotId());
                        expEntrustMore.setExpId(entrustExp.getEntrustExpId());//关联 主异常表id
                        expEntrustMore.setCarrierId(entrustLot.getCarrierId());
                        expEntrustMore.setCarrierName(entrustLot.getCarrierName());
                        expEntrustMore.setCarnoId(entrustLot.getCarnoId());
                        expEntrustMore.setCarno(entrustLot.getCarNo());
                        expEntrustMore.setDriverName(entrustLot.getDriverName());
                        expEntrustMore.setLotId(entrustLot.getEntrustLotId());
                        expEntrustMore.setLotNo(entrustLot.getLot());
                        expEntrustMoreMapper.insertSelective(expEntrustMore);

                        List<PayDetail> writeOffList = new ArrayList<>();
                        //如果扣款那么需要添加一条运单对应的应付明细   承运商扣款标志 INCOME_REMARK 1:赔款
                        //直接新建一条和运单相关联的应付明细记录
                        if(StringUtils.isNotEmpty(lot_vbillno.split("-")[1])) {
                            if(!lot_vbillno.split("-")[1].equals("0")) {
                                //异常扣款金额
                                BigDecimal abnormalFee = new BigDecimal(lot_vbillno.split("-")[1]);

                                List<PayDetail> payDetailList = payDetailService
                                        .selectPayDetailListByLotId(entrustLot.getEntrustLotId());
                                payDetailList = payDetailList.stream()
                                        .filter(x -> PayDetailStatusEnum.NEW.getValue() == x.getVbillstatus()
                                                || PayDetailStatusEnum.AFFIRM.getValue() == x.getVbillstatus())
                                        .sorted(Comparator.comparingInt(x -> {
                                            String costTypeFreight = x.getCostTypeFreight();
                                            // 如果costTypeFreight是1、3、5 油卡，返回1，否则返回0
                                            return ("1".equals(costTypeFreight) || "3".equals(costTypeFreight)
                                                    || "5".equals(costTypeFreight)) ? 1 : 0;
                                        }))
                                        .collect(Collectors.toList());

                                if (abnormalFee.compareTo(BigDecimal.ZERO) != 0 && payDetailList.size() == 0) {
                                    throw new BusinessException("不存“新建”或“已确认”的应付单据，无法异常扣款应付。");
                                }

                                //“新建”或“已确认”的应付合计
                                BigDecimal feeCountAll = payDetailList.stream()
                                        .map(PayDetail::getTransFeeCount)
                                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                                if (abnormalFee.compareTo(feeCountAll) > 0) {
                                    throw new BusinessException("异常扣款应付金额大于“新建”或“已确认”的应付单据金额合计，无法扣款。");
                                }

                                for (PayDetail payDetail : payDetailList) {
                                    if (abnormalFee.compareTo(BigDecimal.ZERO) != 0) {
                                        BigDecimal transFeeCount = payDetail.getTransFeeCount();

                                        if (abnormalFee.compareTo(transFeeCount) > 0) {
                                            //异常扣款金额
                                            abnormalFee = NumberUtil.sub(abnormalFee, transFeeCount);

                                            writeOffList.add(payDetail);
                                        } else if (abnormalFee.compareTo(transFeeCount) == 0) {

                                            abnormalFee = BigDecimal.ZERO;

                                            writeOffList.add(payDetail);
                                        } else if (abnormalFee.compareTo(transFeeCount) < 0) {
                                            PayDetail payDetailInsert = payDetailService.splitPayDetail(payDetail, abnormalFee
                                                    , "EntrustExceptionMoreService.addEntrustException");

                                            writeOffList.add(payDetailInsert);
                                            abnormalFee = BigDecimal.ZERO;
                                        }
                                    }
                                }

                                int isProfitIncluded = Convert.toInt(lot_vbillno.split("-")[2], 0);

                                for (PayDetail payDetail : writeOffList) {
                                    //核销应付
                                    payDetailService.writeOffAbnormalPayDetail(payDetail
                                            , "EntrustExceptionMoreService.addEntrustException", isProfitIncluded);
                                }
                            }
                        }
                    }
                }
            }

            List<AjaxParamsVO.LotIdDedTransFeeVO> lotIdDedTransFeeList = ajaxParamsVO.getLotIdDedTransFeeList();
            for (AjaxParamsVO.LotIdDedTransFeeVO lotIdDedTransFeeVO : lotIdDedTransFeeList) {
                //运单id
                String lotId = lotIdDedTransFeeVO.getLotId();
                //扣款金额
                BigDecimal fee = lotIdDedTransFeeVO.getFee();
                //备注
                String memo = lotIdDedTransFeeVO.getMemo();

                EntrustLot entrustLot = entrustLotMapper.selectEntrustLotById(lotId);
                //查询运单对应的所有委托单
                List<Entrust> entrusts = entrustMapper.selectEntrustByLotId(entrustLot.getEntrustLotId());
                Carrier carrier = carrierMapper.selectCarrierById(entrustLot.getCarrierId());
                if (fee != null && BigDecimal.ZERO.compareTo(fee) != 0) {
                    List<PayDetail> payDetailList = new ArrayList<>();
                    PayDetail payDetail = new PayDetail();
                    payDetail.setPayDetailId(IdUtil.simpleUUID());
                    payDetail.setVbillno(payDetailService.createPayDetailVbillno("1".equals(entrustLot.getIsFleetData()) ? 1 : 0));
                    payDetail.setVbillstatus(1);//应付单状态  默认已确认
                    payDetail.setFreeType("1");//在途费
                    payDetail.setCostTypeOnWay("27"); //在途费用类型 字典获取  异常赔款	27
                    payDetail.setTransFeeCount(fee.negate());//取负数  前台为正数
                    payDetail.setUngotAmount(fee.negate());//取负数  前台为正数
                    payDetail.setLotId(entrustLot.getEntrustLotId());
                    payDetail.setLotno(entrustLot.getLot());
                    payDetail.setCarrierId(entrustLot.getCarrierId());
                    payDetail.setCarrName(entrustLot.getCarrierName());
                    payDetail.setCarrCode(carrier.getCarrCode());
                    payDetail.setBalaCorp(entrustLot.getBalaCorp());
                    payDetail.setBalatype(entrustLot.getBalatype());
                    payDetail.setDriverMobile(entrustLot.getDriverMobile());
                    payDetail.setDriverName(entrustLot.getDriverName());
                    payDetail.setCarno(entrustLot.getCarNo());
                    //payDetail.setReqDeliDate(entrustLot.getReqDeliDate());  //取实际提货时间
                    //payDetail.setReqArriDate(entrustLot.getReqArriDate());  //去实际到货时间
                    payDetail.setRegScrId(pageId);
                    //赔款
                    payDetail.setIncomeRemark(2);
                    payDetail.setIsFleetData(entrustLot.getIsFleetData());
                    //备注
                    payDetail.setMemo(memo);
                    //TODO 业务口的分配车队只能是1对1
                    if (entrusts.size() == 1) {
                        Entrust entrust = entrusts.get(0);
                        if ("0".equals(entrust.getIsFleetData()) && !"0".equals(entrust.getIsFleetAssign())) {//is_Fleet_Data = 0 and  is_Fleet_Assign != 0
                            //找出对应的分配车队发货单
                            Invoice invoiceAll = invoiceMapper.selectInvoiceByBizEntrustId(entrust.getEntrustId());
                            if (invoiceAll != null && !invoiceAll.getVbillstatus().equals("0")) {
                                //不是新建状态需要插入应付
                                ReceiveDetailVO receiveDetail = new ReceiveDetailVO();

                                //拷贝发货单信息
                                BeanUtils.copyBeanProp(receiveDetail, invoiceAll);
                                receiveDetail.setReceiveDetailId(IdUtil.simpleUUID());
                                //结算客户id
                                receiveDetail.setBalaCustomer(invoiceAll.getBalaCustomerId());
                                receiveDetail.setVbillno(receiveDetailService.createReceiveDetailVbillno(1));
                                //客户名称存客户简称
                                receiveDetail.setCustName(invoiceAll.getCustAbbr());
                                //结算方式
                                receiveDetail.setBalatype(invoiceAll.getBalaType());
                                //应收单据状态
                                receiveDetail.setVbillstatus(payDetail.getVbillstatus());
                                receiveDetail.setInvoiceId(invoiceAll.getInvoiceId());
                                //发货单号
                                receiveDetail.setInvoiceVbillno(invoiceAll.getVbillno());
                                //总重量
                                receiveDetail.setFeeWeightCount(invoiceAll.getWeightCount());
                                //是否原始单据 默认原始单据
                                receiveDetail.setVbillType("1");
                                receiveDetail.setDelFlag(0);
                                //结算公司id
                                receiveDetail.setBalaCorp(invoiceAll.getBalaCorpId());
                                receiveDetail.setFreeType(payDetail.getFreeType());
                                //
                                receiveDetail.setTransFeeCount(payDetail.getTransFeeCount());
                                receiveDetail.setRegScrId(pageId);
                                receiveDetail.setCorScrId(pageId);

                                receiveDetail.setIsFleetData("1");
                                receiveDetail.setIsFleetAssign("1");
                                receiveDetail.setFleetPayDetailId(payDetail.getPayDetailId());
                                receiveDetail.setPayoutMark(1); //异常标识

                                receiveDetailService.insertReceiveDetailAndAdjustRecord(receiveDetail, false);
                                //等分配车队的应付数据保存完成  还要回填原来车队应收中对应的应付表的id
                                payDetail.setIsFleetAssign("1");//分配车队的应付保存其状态为1
                                payDetail.setFleetReceiveDetailId(receiveDetail.getReceiveDetailId());
                                /*
                                 * 调整车队的发货单是否加入对账包状态
                                 */
                                List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveByInvoiceId(invoiceAll.getInvoiceId());

                                //已对账的数量
                                long reconciledCount = receiveDetailVOS.stream().filter(x -> ReceiveDetailStatusEnum.RECONCILED.getValue() == x.getVbillstatus()).count();

                                Invoice invoiceFleetUp = new Invoice();
                                invoiceFleetUp.setInvoiceId(invoiceAll.getInvoiceId());
                                if (reconciledCount == receiveDetailVOS.size()) {
                                    //已全部加入对账包
                                    invoiceFleetUp.setIsAddReceCheck(2);
                                    invoiceMapper.updateInvoice(invoiceFleetUp);
                                } else if (reconciledCount < receiveDetailVOS.size() && reconciledCount > 0) {
                                    //部分加入对账包
                                    invoiceFleetUp.setIsAddReceCheck(1);
                                    invoiceMapper.updateInvoice(invoiceFleetUp);
                                } else {
                                    invoiceFleetUp.setIsAddReceCheck(0);
                                    invoiceMapper.updateInvoice(invoiceFleetUp);
                                }
                            } else {
                                //分配车队的应付保存其状态为2 该生成，但对应的车队发货单未确定，所以还未生成应收
                                payDetail.setIsFleetAssign("2");
                            }
                        } else {
                            payDetail.setIsFleetAssign("0");//不是分配车队的数据
                        }
                    }

                    payDetailService.insertPayDetailAndAdjustRecord(payDetail, null, false);
                    //payDetailMapper.insertPayDetail(payDetail);
                    //成本分摊
                    payDetailList.add(payDetail);
                    //entrustList.add(entrust);
                    List<Allocation> allocationByEntrustList = allocationService.getAllocationByEntrustList(payDetailList, entrusts, pageId);
                    for (Allocation allocation : allocationByEntrustList) {
                        allocation.setCostAllocationId(IdUtil.simpleUUID());
                        allocation.setRegDate(new Date());
                        allocation.setRegUserId(shiroUtils.getUserId().toString());
                        allocation.setRegScrId(pageId);
                        allocationMapper.insertAllocation(allocation);
                    }
                }
            }

            InsuranceRecord insuranceRecord = new InsuranceRecord();

            //保存保险&损失
            List<TInsurance> insuranceList = ajaxParamsVO.getInsuranceList();
            for (int j = 0; j < insuranceList.size(); j++) {
                if(!StringUtil.isEmpty(insuranceList.get(j).getLossItem())) {
                    insuranceList.get(j).setRegDate(new Date()); //
                    insuranceList.get(j).setRegUserId(shiroUtils.getUserId().toString());
                    insuranceList.get(j).setRegUserName(shiroUtils.getSysUser().getUserName());
                    insuranceList.get(j).setRegScrId(pageId); //新增画面id
                    insuranceList.get(j).setCorScrId(pageId); //新增画面id
                    insuranceList.get(j).setCorDate(new Date());
                    insuranceList.get(j).setCorUserName(shiroUtils.getSysUser().getUserName());
                    insuranceList.get(j).setCorUserId(shiroUtils.getUserId().toString());

                    insuranceList.get(j).setId(IdUtil.simpleUUID());
                    insuranceList.get(j).setExpId(entrustExp.getEntrustExpId());
                    insuranceMapper.insertSelective(insuranceList.get(j));

                    BigDecimal amount = insuranceList.get(j).getLossAmount();
                    if (amount == null) {
                        amount = BigDecimal.ZERO;
                    }

                    switch (insuranceList.get(j).getLossItem()) {
                        case "货损":
                            insuranceRecord.setAccidentAmount(
                                    insuranceRecord.getAccidentAmount() == null
                                            ? amount
                                            : insuranceRecord.getAccidentAmount().add(amount)
                            );
                            break;
                        case "二次运输费":
                            insuranceRecord.setSecondaryTransportationFee(
                                    insuranceRecord.getSecondaryTransportationFee() == null
                                            ? amount
                                            : insuranceRecord.getSecondaryTransportationFee().add(amount)
                            );
                            break;
                        case "驳货费":
                            insuranceRecord.setTransshipmentFee(
                                    insuranceRecord.getTransshipmentFee() == null
                                            ? amount
                                            : insuranceRecord.getTransshipmentFee().add(amount)
                            );
                            break;
                        case "律师费":
                            insuranceRecord.setLawyerFee(
                                    insuranceRecord.getLawyerFee() == null
                                            ? amount
                                            : insuranceRecord.getLawyerFee().add(amount)
                            );
                            break;
                        case "律师差旅费":
                            insuranceRecord.setLawyerTravelFee(
                                    insuranceRecord.getLawyerTravelFee() == null
                                            ? amount
                                            : insuranceRecord.getLawyerTravelFee().add(amount)
                            );
                            break;
                        case "诉讼费":
                            insuranceRecord.setLitigationFee(
                                    insuranceRecord.getLitigationFee() == null
                                            ? amount
                                            : insuranceRecord.getLitigationFee().add(amount)
                            );
                            break;
                        case "其他费":
                            insuranceRecord.setOtherFees(
                                    insuranceRecord.getOtherFees() == null
                                            ? amount
                                            : insuranceRecord.getOtherFees().add(amount)
                            );
                            break;
                        case "保险赔付":
                            insuranceRecord.setInsuranceCompensationAmount(
                                    insuranceRecord.getInsuranceCompensationAmount() == null
                                            ? amount
                                            : insuranceRecord.getInsuranceCompensationAmount().add(amount)
                            );
                            break;
                        case "残值":
                            insuranceRecord.setResidualValue(
                                    insuranceRecord.getResidualValue() == null
                                            ? amount
                                            : insuranceRecord.getResidualValue().add(amount)
                            );
                            break;
                        case "承运商赔付":
                            insuranceRecord.setCarrierCompensationAmount(
                                    insuranceRecord.getCarrierCompensationAmount() == null
                                            ? amount
                                            : insuranceRecord.getCarrierCompensationAmount().add(amount)
                            );
                            break;
                        case "其他赔付":
                            insuranceRecord.setOtherPayout(
                                    insuranceRecord.getOtherPayout() == null
                                            ? amount
                                            : insuranceRecord.getOtherPayout().add(amount)
                            );
                            break;
                        default:
                            break;
                    }
                }
            }
            //不为空则需要插入T_INSURANCE_RECORD
            if (StringUtils.isNotEmpty(entrustExp.getInsurancePolicyId())) {
                insuranceRecord.setId(IdUtil.simpleUUID());
                insuranceRecord.setInsurancePolicyId(entrustExp.getInsurancePolicyId());
                insuranceRecord.setEntrustExpId(entrustExp.getEntrustExpId());
                insuranceRecord.setInsuranceDate(entrustExp.getAccidentTime());
                insuranceRecord.setClaimNumber(entrustExp.getReportNo());
                insuranceRecord.setRegScrId(pageId);

                //保险赔付金额+承运商赔付金额+其他赔付+残值
                BigDecimal add = NumberUtil.add(insuranceRecord.getInsuranceCompensationAmount(),
                        insuranceRecord.getCarrierCompensationAmount(),
                        insuranceRecord.getOtherPayout(), insuranceRecord.getResidualValue());
                //货损+二次运输费用+驳货费+律师费+律师差旅费+诉讼费+其他费
                BigDecimal add1 = NumberUtil.add(insuranceRecord.getAccidentAmount(),
                        insuranceRecord.getSecondaryTransportationFee(),
                        insuranceRecord.getTransshipmentFee(),
                        insuranceRecord.getLawyerFee(),
                        insuranceRecord.getLawyerTravelFee(),
                        insuranceRecord.getLitigationFee(),
                        insuranceRecord.getOtherFees());

                insuranceRecord.setCompanyProfitLoss(NumberUtil.sub(add, add1));
                insuranceRecordMapper.insertSelective(insuranceRecord);

            }
            //保存异常跟踪
            List<TExceptionTrack> exceptionTrackList = ajaxParamsVO.getExceptionTrackList();
            for (int j = 0; j < exceptionTrackList.size(); j++) {
                if(!StringUtil.isEmpty(exceptionTrackList.get(j).getAssistanceContent())) {
                    exceptionTrackList.get(j).setRegDate(new Date()); //
                    exceptionTrackList.get(j).setRegUserId(shiroUtils.getUserId().toString());
                    exceptionTrackList.get(j).setRegUserName(shiroUtils.getSysUser().getUserName());
                    exceptionTrackList.get(j).setRegScrId(pageId); //新增画面id
                    exceptionTrackList.get(j).setCorScrId(pageId); //新增画面id
                    exceptionTrackList.get(j).setCorDate(new Date());
                    exceptionTrackList.get(j).setCorUserName(shiroUtils.getSysUser().getUserName());
                    exceptionTrackList.get(j).setCorUserId(shiroUtils.getUserId().toString());

                    exceptionTrackList.get(j).setId(IdUtil.simpleUUID());
                    exceptionTrackList.get(j).setExpId(entrustExp.getEntrustExpId());
                    exceptionTrackMapper.insertSelective(exceptionTrackList.get(j));
                }
            }
        }else {
            entrustExp.setRegDate(new Date());
            entrustExp.setRegUserId(shiroUtils.getUserId().toString());
            entrustExp.setRegUserName(shiroUtils.getSysUser().getUserName());
            entrustExp.setRegScrId(pageId); //新增画面id
            entrustExp.setCorScrId(pageId);
            entrustExp.setCorDate(new Date());
            entrustExp.setCorUserName(shiroUtils.getSysUser().getUserName());
            entrustExp.setCorUserId(shiroUtils.getUserId().toString());
            entrustExp.setEntrustExpId(IdUtil.simpleUUID());
            entrustExp.setCaseStatus("1");   //案件状态，1：新建
            entrustExp.setIsCustomerException((short) 2);
            i = entrustExpMapper.insertEntrustExp(entrustExp);
            //保存损失金额记录
            if(entrustExp.getLossAmount() != null && entrustExp.getLossAmount().compareTo(BigDecimal.ZERO) != 0) {
                EntrustExpLossRecord entrustExpLossRecord = new EntrustExpLossRecord();
                entrustExpLossRecord.setId(IdUtil.simpleUUID());
                entrustExpLossRecord.setEntrustExpId(entrustExp.getEntrustExpId());
                entrustExpLossRecord.setRegUserName(shiroUtils.getSysUser().getUserName());
                entrustExpLossRecord.setLossAmount(entrustExp.getLossAmount());
                entrustExpMapper.insertEntrustExpLossRecord(entrustExpLossRecord);
            }
            //保存主异常和异常类型的关联表
            String expType = entrustExp.getExpType();
            String[] split_expType = expType.split(",");
            for (String s : split_expType) {
                TExceptionDict exceptionDict = new TExceptionDict();
                exceptionDict.setId(IdUtil.simpleUUID());
                exceptionDict.setEntrustExpId(entrustExp.getEntrustExpId());
                exceptionDict.setDictCode((Short.parseShort(s)));
                exceptionDictMapper.insertSelective(exceptionDict);
            }
            //保存异常跟踪
            List<TExceptionTrack> exceptionTrackList = ajaxParamsVO.getExceptionTrackList();
            for (int j = 0; j < exceptionTrackList.size(); j++) {
                if(!StringUtil.isEmpty(exceptionTrackList.get(j).getAssistanceContent())) {
                    exceptionTrackList.get(j).setRegDate(new Date()); //
                    exceptionTrackList.get(j).setRegUserId(shiroUtils.getUserId().toString());
                    exceptionTrackList.get(j).setRegUserName(shiroUtils.getSysUser().getUserName());
                    exceptionTrackList.get(j).setRegScrId(pageId); //新增画面id
                    exceptionTrackList.get(j).setCorScrId(pageId); //新增画面id
                    exceptionTrackList.get(j).setCorDate(new Date());
                    exceptionTrackList.get(j).setCorUserName(shiroUtils.getSysUser().getUserName());
                    exceptionTrackList.get(j).setCorUserId(shiroUtils.getUserId().toString());

                    exceptionTrackList.get(j).setId(IdUtil.simpleUUID());
                    exceptionTrackList.get(j).setExpId(entrustExp.getEntrustExpId());
                    exceptionTrackMapper.insertSelective(exceptionTrackList.get(j));
                }
            }
        }
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addReceiveDetailWithException(ReceiveDetailException receiveDetailException) {
        //1.填写金额不能大于该单的应收明细
        BigDecimal payOutAmount = receiveDetailException.getPayOutAmount();

        String receiveDetailId = receiveDetailException.getReceiveDetailId();
        ReceiveDetailVO receiveDetailVO = receiveDetailService.selectReceiveById(receiveDetailId);
        BigDecimal transFeeCount = receiveDetailVO.getTransFeeCount();
        if(payOutAmount.compareTo(transFeeCount) > 1) {
            throw new BusinessException(com.ruoyi.common.utils.StringUtils.format("赔款金额已超出应付明细!"));
        }
        //2.填写金额不能大于该发货单下的应收明细总额

        //3.查询发货单信息
        String invoiceId = receiveDetailException.getInvoiceId();
        Invoice invoice = invoiceMapper.selectInvoiceById(invoiceId);

        ReceiveDetailVO receiveDetailVO1 = new ReceiveDetailVO();
        //拷贝发货单信息
        BeanUtils.copyBeanProp(receiveDetailVO1, invoice);
        receiveDetailVO1.setReceiveDetailId(IdUtil.simpleUUID());
        //TODO  插入t_receive_detail
        int i = receiveDetailService.insertReceiveDetail(receiveDetailVO1);
        return i > 0;
    }

    @Override
    public boolean addResidualValueTracking(ResidualValueTrackingVO residualValueTrackingListVO) {
        List<ResidualValueTracking> residualValueTracking1 = residualValueTrackingListVO.getResidualValueTrackingPage();
        int i = 0;
        for (ResidualValueTracking residualValueTracking: residualValueTracking1) {
            if(StringUtils.isNotEmpty(residualValueTracking.getId())) {
                i = residualValueTrackingMapper.updateByPrimaryKeySelective(residualValueTracking);
            }else {
                residualValueTracking.setId(IdUtil.simpleUUID());
                i = residualValueTrackingMapper.insertSelective(residualValueTracking);
            }
        }
        /*int i = residualValueTrackingMapper.batchInsert(residualValueTracking);*/
        return i > 0;
    }

    @Override
    public List<ResidualValueTracking> residualValueTrackingPage(String expId) {
        List<ResidualValueTracking> residualValueTrackings = residualValueTrackingMapper.selectResidualValueTrackingPage(expId);
        return residualValueTrackings;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addExceptionCorrection(ExceptionCorrectionVO exceptionCorrectionVO) {
        exceptionCorrectionVO.setId(IdUtil.simpleUUID());
        int i = exceptionCorrectionMapper.insertSelective(exceptionCorrectionVO);
        //添加考核人员
        List<MAppraisers> appraisersList = exceptionCorrectionVO.getAppraisersList();
        for (MAppraisers mAppraisers :appraisersList) {
            if(StringUtils.isNotEmpty(mAppraisers.getName()) && (mAppraisers.getAmount() != null)) {
                mAppraisers.setId(IdUtil.simpleUUID());
                mAppraisers.setExceptionCorrectionId(exceptionCorrectionVO.getId());
                int i1 = appraisersMapper.insertSelective(mAppraisers);
            }
        }
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExceptionCorrection(ExceptionCorrectionVO exceptionCorrectionVO) {
        int i = exceptionCorrectionMapper.updateByPrimaryKeySelective(exceptionCorrectionVO);
        //先删除
        MAppraisersExample appraisersExample = new MAppraisersExample();
        appraisersExample.createCriteria().andExceptionCorrectionIdEqualTo(exceptionCorrectionVO.getId());
        int i2 = appraisersMapper.deleteByExample(appraisersExample);
        //修改考核人员
        int i1 = 0;
        List<MAppraisers> appraisersList = exceptionCorrectionVO.getAppraisersList();
        for (MAppraisers mAppraisers :appraisersList) {
            if(StringUtils.isNotEmpty(mAppraisers.getName()) && (mAppraisers.getAmount() != null)) {
                mAppraisers.setId(IdUtil.simpleUUID());
                mAppraisers.setExceptionCorrectionId(exceptionCorrectionVO.getId());
                i1 = appraisersMapper.insertSelective(mAppraisers);
            }
        }
        return i > 0;
    }
}
